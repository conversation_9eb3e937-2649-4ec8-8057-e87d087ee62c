/**
 * ChatInput component for message input with command history
 */

import React, { useState, useRef, useEffect } from 'react';
import { Send, History } from 'lucide-react';
import { ChatInputProps } from '@/types';
import { COMMON_COMMANDS } from '@/constants';
import { findCommandCompletions } from '@/utils';

const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSubmit,
  onKeyDown,
  disabled,
  placeholder,
  commandHistory,
  historyIndex
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update suggestions when input changes
  useEffect(() => {
    if (value.trim().length > 0) {
      const historySuggestions = findCommandCompletions(value, commandHistory);
      const commonSuggestions = findCommandCompletions(value, COMMON_COMMANDS);
      const allSuggestions = [...new Set([...historySuggestions, ...commonSuggestions])];

      setSuggestions(allSuggestions.slice(0, 5)); // Limit to 5 suggestions
      setShowSuggestions(allSuggestions.length > 0);
      setSelectedSuggestion(-1);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
    }
  }, [value, commandHistory]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle suggestions navigation
    if (showSuggestions && suggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestion(prev =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        return;
      }

      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestion(prev =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        return;
      }

      if (e.key === 'Tab' && selectedSuggestion >= 0) {
        e.preventDefault();
        onChange(suggestions[selectedSuggestion]);
        setShowSuggestions(false);
        return;
      }

      if (e.key === 'Escape') {
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedSuggestion(-1);
        return;
      }
    }

    // Pass through to parent handler for history navigation and submit
    onKeyDown(e);
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleSubmit = () => {
    if (showSuggestions && selectedSuggestion >= 0) {
      onChange(suggestions[selectedSuggestion]);
      setShowSuggestions(false);
    } else {
      onSubmit();
    }
  };

  return (
    <div className="relative">
      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-theme-primary border border-theme-primary rounded-xl shadow-2xl max-h-40 overflow-y-auto z-50 transition-all duration-300 backdrop-blur-sm">
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion}
              className={`px-3 py-2 cursor-pointer text-sm border-b border-theme-primary last:border-b-0 transition-all duration-200 ${
                index === selectedSuggestion
                  ? 'bg-surface-secondary text-accent-primary'
                  : 'text-theme-secondary hover:bg-surface-primary'
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className="flex items-center gap-2">
                {commandHistory.includes(suggestion) ? (
                  <History size={12} className="text-accent-primary" />
                ) : (
                  <span className="w-3 h-3 bg-theme-tertiary rounded-full flex-shrink-0" />
                )}
                <span className="truncate">{suggestion}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Input container */}
      <div className="flex items-center gap-2">
        {/* History indicator */}
        {historyIndex >= 0 && (
          <div className="flex items-center gap-1 text-xs text-theme-tertiary bg-surface-secondary px-2 py-1 rounded-full border border-theme-primary">
            <History size={12} />
            <span>{historyIndex + 1}/{commandHistory.length}</span>
          </div>
        )}

        {/* Input field */}
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full px-4 py-2 border border-theme-secondary bg-theme-primary text-theme-primary rounded-full focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary disabled:bg-surface-tertiary disabled:text-theme-quaternary disabled:cursor-not-allowed text-base transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg"
          />


        </div>

        {/* Submit button */}
        <button
          onClick={handleSubmit}
          disabled={disabled || value.trim().length === 0}
          className="p-2 bg-accent-primary text-white rounded-full hover:opacity-90 hover:scale-105 disabled:bg-theme-quaternary disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent-primary focus:ring-offset-2 focus:ring-offset-theme-primary shadow-lg hover:shadow-xl"
        >
          <Send size={16} />
        </button>
      </div>


    </div>
  );
};

export default ChatInput;
