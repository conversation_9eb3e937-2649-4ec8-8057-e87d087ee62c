/**
 * TaskHistoryPanel component for task history and management
 */

import React from 'react';
import { History, Plus, Clock, CheckCircle, XCircle, Pause, Play } from 'lucide-react';
import { motion } from 'framer-motion';
import { TaskHistoryPanelProps } from '@/types';

const TaskHistoryPanel: React.FC<TaskHistoryPanelProps> = ({
  tasks,
  onTaskSelect,
  onNewTask
}) => {
  const getStatusIcon = (status: 'pending' | 'running' | 'completed' | 'failed' | 'paused') => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={14} className="text-theme-secondary" />;
      case 'failed':
        return <XCircle size={14} className="text-theme-secondary" />;
      case 'running':
        return <Play size={14} className="text-theme-secondary" />;
      case 'paused':
        return <Pause size={14} className="text-theme-secondary" />;
      case 'pending':
        return <Clock size={14} className="text-theme-secondary" />;
      default:
        return <Clock size={14} className="text-theme-secondary" />;
    }
  };

  const formatTime = (date?: Date) => {
    if (!date) return 'Unknown';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="flex flex-col h-full">
      {/* Panel Header */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
          <History size={16} className="text-accent-primary" />
          Task History
        </h3>
      </div>

      {/* Task List - Scrollable */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden minimal-scrollbar">
        <div className="space-y-2">
          {tasks.length === 0 ? (
            <div className="text-center py-6 text-theme-tertiary">
              <History size={24} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No tasks yet</p>
            </div>
          ) : (
            tasks.map((task) => (
              <motion.button
                key={task.id}
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
                onClick={() => onTaskSelect(task.id)}
                className="w-full p-3 rounded-lg border border-theme-primary transition-all duration-200 text-left hover:bg-surface-secondary"
              >
                <div className="space-y-2 min-w-0">
                  {/* Task Header */}
                  <div className="flex items-start justify-between gap-2 min-w-0">
                    <div className="min-w-0 flex-1">
                      <div className="font-medium text-theme-primary text-sm truncate">
                        {task.title}
                      </div>
                      <div className="text-xs text-theme-tertiary truncate">
                        {task.description}
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      {getStatusIcon(task.status)}
                    </div>
                  </div>

                  {/* Task Metadata */}
                  <div className="flex items-center justify-between text-xs text-theme-tertiary min-w-0">
                    <span className="truncate">{formatTime(task.startTime)}</span>
                    {task.totalCommands > 0 && (
                      <span className="flex-shrink-0 ml-2">{task.successfulCommands}/{task.totalCommands}</span>
                    )}
                  </div>
                </div>
              </motion.button>
            ))
          )}
        </div>
      </div>

      {/* New Task Button - Sticky Bottom */}
      <div className="mt-3 pt-3 border-t border-theme-primary">
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={onNewTask}
          className="w-full p-3 border border-dashed border-theme-primary hover:border-accent-primary rounded-lg transition-all duration-200 group hover:bg-surface-secondary"
        >
          <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
            <Plus size={16} />
            <span className="text-sm font-medium">New Task</span>
          </div>
        </motion.button>
      </div>
    </div>
  );
};

export default TaskHistoryPanel;
